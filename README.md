![Repo Banner](https://i.imgur.com/cHkkmpg.png)

# Cinemapedia

A simple project using The Movie DB based on Flutter, Clean architecture and material designs & animations.

> **Note:** This project is not completed yet, as soon as possible when it's complete I will update it here. Stay tuned for more exciting updates!

[![Made-with-flutter](https://img.shields.io/badge/Made%20with-Flutter-orange)](https://flutter.dev/) 
![GitHub repo size](https://img.shields.io/github/repo-size/ivansaul/flutter_cinemapedia_app)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

### Don't forget to ⭐ the repo
![GitHub stars](https://img.shields.io/github/stars/ivansaul/flutter_cinemapedia_app?style=social)
![GitHub forks](https://img.shields.io/github/forks/ivansaul/flutter_cinemapedia_app?style=social) 

## Features

- [x] Dark Mode
- [x] Home Screen
- [x] Favorite Movies
- [x] Top Rated Movies
- [x] Reviews and ratings
- [x] Movie Detail Screen
- [x] Search Results Screen
- [ ] Select by Categories
- [ ] Login Screen
- [ ] welcome screen
- [ ] Splash Screen
- [ ] Share Screen
- [ ] Light Mode
- [ ] Languages
- [ ] Notification
- [ ] Profile Screen

and much more...
Check it yourself :)

## Screenshots

<p align="center">
  <img src="https://i.imgur.com/HPArEvf.png" alt="Screenshots">
  <img src="https://i.imgur.com/IO1UrQX.png" alt="Screenshots">
</p>

## Demo

https://github.com/ivansaul/flutter_cinemapedia_app/assets/********/03f4bdd4-905e-4be7-baac-bcfc41c04719

## Packages we are using:

Package | Usage
------------ | -------------
[dio](https://pub.dev/packages/dio) | A powerful HTTP package for Dart/Flutter.
[isar](https://pub.dev/packages/isar) | Extremely fast, easy to use, and fully async NoSQL database for Flutter.
[riverpod](https://pub.dev/packages/riverpod) | A simple way to access state from anywhere in your application.
[go_router](https://pub.dev/packages/go_router) | A declarative routing package for Flutter that uses the Router API to provide a convenient, url-based API for navigating between different screens.
[flutter_svg](https://pub.dev/packages/flutter_svg) | Draw SVG files using Flutter..
[google_fonts](https://pub.dev/packages/google_fonts) | A Flutter package to use fonts from fonts.google.com.
[stylish_bottom_bar](https://pub.dev/packages/stylish_bottom_bar) | A collection of stylish bottom navigation bars.


## Design
- [Movies UI - Figma](https://www.figma.com/community/file/1088719884686291024)
- [Actors UI - Figma](https://www.figma.com/community/file/1158921876927860533)

## Building from Source

1. If you don't have Flutter SDK installed, please visit official [Flutter](https://flutter.dev/) site.
2. Fetch latest source code from master branch.

```
git clone https://github.com/ivansaul/flutter_cinemapedia_app.git
```

3. Run the app with Android Studio or VS Code. Or the command line:

```
flutter pub get
flutter run
```

## Contribute

Contributions are welcome.

## Facing any Issue?

Feel free to open an Issue :)
