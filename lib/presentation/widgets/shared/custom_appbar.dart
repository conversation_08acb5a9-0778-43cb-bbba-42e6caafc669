import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';

import '../../../config/theme/app_theme.dart';

class CustomAppBar extends StatelessWidget {
  const CustomAppBar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      systemOverlayStyle: const SystemUiOverlayStyle(),
      backgroundColor: AppTheme.primaryDark,
      // pinned: true,
      floating: true,
      flexibleSpace: Padding(
        padding: const EdgeInsets.only(top: 16, left: 24, right: 24),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  fit: BoxFit.cover,
                  // image: NetworkImage('https://i.imgur.com/ud3Owj9.jpg'),
                  image: NetworkImage('https://i.imgur.com/0GG1209.jpg'),
                ),
                shape: BoxShape.circle,
                border: GradientBoxBorder(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Color(0xff19A1BE), Color(0xff7D4192)],
                  ),
                  width: 4,
                ),
                // border: Border.all(
                //   strokeAlign: BorderSide.strokeAlignOutside,
                //   color: AppTheme.primarySoft,
                //   width: 4,
                // ),
                // borderRadius: BorderRadius.circular(16),
              ),
            ),
            const SizedBox(width: 10),
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Hello, Maria',
                  style: AppTheme.h4Semibold,
                ),
                Text(
                  'Welcome back',
                  style: AppTheme.h6Semibold
                      .copyWith(color: AppTheme.textColorGrey),
                ),
              ],
            ),
            const Spacer(),
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.primarySoft,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: IconButton(
                  onPressed: () {},
                  icon: const Icon(
                    Icons.widgets,
                    color: Colors.white,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
